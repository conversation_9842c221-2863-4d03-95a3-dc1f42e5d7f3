package io.wyden.pricing.service.quoting;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.marketdata.InstrumentKey;
import io.wyden.published.marketdata.MarketDataEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Nullable;

import static io.wyden.pricing.Util.noneNull;
import static org.apache.commons.lang3.ObjectUtils.firstNonNull;
import static org.apache.commons.lang3.ObjectUtils.getFirstNonNull;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class BidAskQuoteAggregator {

    private static final Logger LOGGER = LoggerFactory.getLogger(BidAskQuoteAggregator.class);

    private final Map<InstrumentKey, BidAskQuote> lastSeenQuotes;

    public BidAskQuoteAggregator() {
        this.lastSeenQuotes = new HashMap<>();
    }

    void update(MarketDataEvent marketDataEvent) {
        InstrumentKey instrumentKey = InstrumentKey.newBuilder()
            .setInstrumentId(marketDataEvent.getIdentifier().getInstrumentId())
            .setVenueAccount(marketDataEvent.getIdentifier().getVenueAccount())
            .build();

        lastSeenQuotes.compute(instrumentKey, (ik, prev) -> {
            String eventUpdateTime = marketDataEvent.getIdentifier().getDateTime();
            ZonedDateTime updateTime = eventUpdateTime.isBlank() ? ZonedDateTime.now() : DateUtils.isoUtcTimeToZonedDateTime(eventUpdateTime);
            BigDecimal bidPrice = getBidPrice(marketDataEvent);
            BigDecimal askPrice = getAskPrice(marketDataEvent);
            BigDecimal bidSize = getBidSize(marketDataEvent);
            BigDecimal askSize = getAskSize(marketDataEvent);

            Quote currentBid = noneNull(bidPrice, bidSize) ? new Quote(bidPrice, bidSize) : prev != null ? prev.bid() : null;
            Quote currentAsk = noneNull(askPrice, askSize) ? new Quote(askPrice, askSize) : prev != null ? prev.ask() : null;
            return new BidAskQuote(updateTime, currentBid, currentAsk);
        });
    }

    @Nullable
    private BigDecimal getBidPrice(MarketDataEvent marketDataEvent) {
        return getFirstNonNull(
            () -> tryParse(marketDataEvent.getBid().getPrice()),
            () -> tryParse(marketDataEvent.getBidAskQuote().getBidPrice()),
            () -> tryParse(marketDataEvent.getOrderBook().getTopBid().getPrice()));
    }

    @Nullable
    private BigDecimal getBidSize(MarketDataEvent marketDataEvent) {
        return getFirstNonNull(
            () -> tryParse(marketDataEvent.getBid().getSize()),
            () -> tryParse(marketDataEvent.getBidAskQuote().getBidSize()),
            () -> tryParse(marketDataEvent.getOrderBook().getTopBid().getAmount()));
    }

    @Nullable
    private BigDecimal getAskPrice(MarketDataEvent marketDataEvent) {
        return getFirstNonNull(
            () -> tryParse(marketDataEvent.getAsk().getPrice()),
            () -> tryParse(marketDataEvent.getBidAskQuote().getAskPrice()),
            () -> tryParse(marketDataEvent.getOrderBook().getTopAsk().getPrice()));
    }

    @Nullable
    private BigDecimal getAskSize(MarketDataEvent marketDataEvent) {
        return getFirstNonNull(
            () -> tryParse(marketDataEvent.getAsk().getSize()),
            () -> tryParse(marketDataEvent.getBidAskQuote().getAskSize()),
            () -> tryParse(marketDataEvent.getOrderBook().getTopAsk().getAmount()));
    }

    @Nullable
    private BigDecimal tryParse(String val) {
        try {
            return isNotBlank(val) ? new BigDecimal(val) : null;
        } catch (Exception e) {
            LOGGER.warn("Unable to parse market data event value: {}", val, e);
            return null;
        }
    }

    @Nullable
    BidAskQuote aggregate(Duration ttl) {
        return lastSeenQuotes.values().stream()
            .filter(quote -> quote.isValid(ttl))
            .reduce((q1, q2) -> {
                ZonedDateTime updateTime = (ZonedDateTime) getHigherNonNull(q1.updateTime(), q2.updateTime());
                Quote bid = getHigherNonNull(q1.bid(), q2.bid());
                Quote ask = getLowerNonNull(q1.ask(), q2.ask());
                return new BidAskQuote(updateTime, bid, ask);
            }).orElse(null);
    }

    private <T extends Comparable<T>> T getHigherNonNull(T t1, T t2) {
        return noneNull(t1, t2) ? t1.compareTo(t2) > 0 ? t1 : t2 : firstNonNull(t1, t2);
    }
    private <T extends Comparable<T>> T getLowerNonNull(T t1, T t2) {
        return noneNull(t1, t2) ? t1.compareTo(t2) < 0 ? t1 : t2 : firstNonNull(t1, t2);
    }
}
