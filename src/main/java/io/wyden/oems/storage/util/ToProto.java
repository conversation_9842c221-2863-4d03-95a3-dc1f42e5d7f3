package io.wyden.oems.storage.util;

import com.hazelcast.org.codehaus.commons.nullanalysis.NotNull;
import com.hazelcast.org.codehaus.commons.nullanalysis.Nullable;
import io.wyden.cloudutils.tools.DateUtils;
import org.apache.logging.log4j.util.Strings;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ToProto {

    @NotNull
    public static String mapString(@Nullable String value) {
        return Objects.requireNonNullElse(value, Strings.EMPTY);
    }

    @NotNull
    public static String mapBigDecimal(@Nullable BigDecimal value) {
        if (value == null) {
            return Strings.EMPTY;
        } else {
            return value.toPlainString();
        }
    }

    @NotNull
    public static String mapZonedDateTime(@Nullable ZonedDateTime value) {
        return DateUtils.toIsoUtcTime(value);
    }

    @NotNull
    public static String getTimeString(ZonedDateTime zonedDateTime) {
        return DateUtils.toFixUtcTime(zonedDateTime);
    }

    @NotNull
    public static Map<String, String> fromCsvStringToMap(@Nullable String tags) {
        if (tags == null || tags.isBlank()) {
            return Map.of();
        }

        return Arrays.stream(tags.split(","))
                .map(entry -> entry.split("="))
                .collect(Collectors.toMap(entry -> entry[0], entry -> entry[1]));
    }

    public static List<String> fromCsvStringToList(String string) {
        if (string == null || string.isBlank()) {
            return List.of();
        }

        return Arrays.stream(string.split(","))
            .collect(Collectors.toList());
    }

    public static <T> List<T> fromCsvStringToList(String string, Function<String, T> fromStringFn) {
        if (string == null || string.isBlank()) {
            return List.of();
        }

        return Arrays.stream(string.split(","))
            .map(fromStringFn)
            .collect(Collectors.toList());
    }
}
