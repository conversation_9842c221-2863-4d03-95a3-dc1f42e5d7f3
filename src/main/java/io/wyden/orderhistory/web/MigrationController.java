package io.wyden.orderhistory.web;

import io.wyden.orderhistory.service.OrderStateMigrationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/migration")
public class MigrationController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MigrationController.class);

    private final OrderStateMigrationService migrationService;

    public MigrationController(OrderStateMigrationService migrationService) {
        this.migrationService = migrationService;
    }

    @PostMapping("/start")
    public ResponseEntity<String> startMigration() {
        LOGGER.warn("Migration start requested");
        
        try {
            migrationService.migrateOrderStates();
            return ResponseEntity.ok("Migration completed successfully");
        } catch (Exception e) {
            LOGGER.error("Migration failed", e);
            return ResponseEntity.internalServerError()
                .body("Migration failed: " + e.getMessage());
        }
    }
}